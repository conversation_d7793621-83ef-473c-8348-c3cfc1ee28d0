package main

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/initializer"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	repo_activity "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/activity_cashback"
	service_activity "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/activity_cashback"
)

func main() {
	// Set environment variables for database connection
	os.Setenv("POSTGRES_AGENCY_HOST", "127.0.0.1")
	os.Setenv("POSTGRES_AGENCY_PORT", "5433")
	os.Setenv("POSTGRES_DB", "agent")
	os.Setenv("POSTGRES_AGENCY_USER", "postgres")
	os.Setenv("POSTGRES_AGENCY_PASS", "postgres")

	// Initialize configuration and database
	global.GVA_VP = initializer.Viper()
	global.GVA_LOG = initializer.Zap()
	global.GVA_DB = initializer.Gorm()
	initializer.DBList()

	if global.GVA_DB == nil {
		log.Fatal("Failed to connect to database")
	}

	ctx := context.Background()

	// New user ID from JWT
	userIDStr := "2f548b90-e9d8-4300-969f-b9896da7472a"
	taskIDStr := "dba8d2e3-bc0d-41f3-bf4f-98b5c5b68d83"

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		log.Fatalf("Invalid user ID: %v", err)
	}

	taskID, err := uuid.Parse(taskIDStr)
	if err != nil {
		log.Fatalf("Invalid task ID: %v", err)
	}

	fmt.Printf("Debugging NEW USER task completion for:\n")
	fmt.Printf("Task ID: %s\n", taskID)
	fmt.Printf("User ID: %s\n", userID)
	fmt.Printf("=================================\n\n")

	// Check if user exists
	var user model.User
	err = global.GVA_DB.First(&user, "id = ?", userID).Error
	if err != nil {
		fmt.Printf("❌ User not found: %v\n", err)

		// Create user for testing
		fmt.Printf("🔄 Creating user for testing...\n")
		newUser := model.User{
			ID:    userID,
			Email: stringPtr("<EMAIL>"),
		}

		if err := global.GVA_DB.Create(&newUser).Error; err != nil {
			fmt.Printf("❌ Failed to create user: %v\n", err)
			return
		} else {
			fmt.Printf("✅ User created successfully\n")
		}
	} else {
		email := "N/A"
		if user.Email != nil {
			email = *user.Email
		}
		fmt.Printf("✅ User found: %s\n", email)
	}

	// Check if task exists
	var task model.ActivityTask
	err = global.GVA_DB.First(&task, "id = ?", taskID).Error
	if err != nil {
		fmt.Printf("❌ Task not found: %v\n", err)
		return
	}

	fmt.Printf("✅ Task found: %s (%s)\n", task.Name, task.TaskType)
	fmt.Printf("   Points: %d, Active: %t\n", task.Points, task.IsActive)

	// Check user task progress
	var progress model.UserTaskProgress
	err = global.GVA_DB.First(&progress, "user_id = ? AND task_id = ?", userID, taskID).Error
	if err != nil {
		fmt.Printf("❌ Task progress not found: %v\n", err)
		fmt.Printf("🔄 This is expected for new users\n")
	} else {
		fmt.Printf("✅ Task progress found: Status=%s, Value=%d\n",
			progress.Status, progress.ProgressValue)
		if progress.LastCompletedAt != nil {
			fmt.Printf("   Last completed at: %s\n", progress.LastCompletedAt.Format("2006-01-02 15:04:05"))
		}
	}

	// Test the actual CompleteTask flow with services
	fmt.Printf("\n🧪 Testing CompleteTask with services...\n")

	// Create repositories and services
	taskRepo := repo_activity.NewActivityTaskRepository()
	categoryRepo := repo_activity.NewTaskCategoryRepository()
	progressRepo := repo_activity.NewUserTaskProgressRepository()
	historyRepo := repo_activity.NewTaskCompletionHistoryRepository()

	// Create tier service
	tierInfoRepo := repo_activity.NewUserTierInfoRepository()
	tierBenefitRepo := repo_activity.NewTierBenefitRepository()
	tierService := service_activity.NewTierManagementService(tierInfoRepo, tierBenefitRepo)

	// Create progress service
	progressService := service_activity.NewTaskProgressService(progressRepo, taskRepo, historyRepo)

	// Create task management service
	taskService := service_activity.NewTaskManagementService(
		taskRepo, categoryRepo, progressRepo, historyRepo, tierService, progressService)

	// Test CompleteTask
	verificationData := map[string]interface{}{
		"test": true,
	}

	err = taskService.CompleteTask(ctx, userID, taskID, verificationData)
	if err != nil {
		fmt.Printf("❌ CompleteTask failed: %v\n", err)

		// Let's debug step by step
		fmt.Printf("\n🔍 Debugging step by step...\n")

		// Test GetTaskProgress directly
		fmt.Printf("1. Testing GetTaskProgress...\n")
		_, err = progressService.GetTaskProgress(ctx, userID, taskID)
		if err != nil {
			fmt.Printf("   ❌ GetTaskProgress failed: %v\n", err)

			// Test InitializeTaskProgress
			fmt.Printf("2. Testing InitializeTaskProgress...\n")
			newProgress, err := progressService.InitializeTaskProgress(ctx, userID, taskID)
			if err != nil {
				fmt.Printf("   ❌ InitializeTaskProgress failed: %v\n", err)
			} else {
				fmt.Printf("   ✅ InitializeTaskProgress succeeded: Status=%s\n", newProgress.Status)
			}
		} else {
			fmt.Printf("   ✅ GetTaskProgress succeeded\n")
		}
	} else {
		fmt.Printf("✅ CompleteTask succeeded!\n")

		// Check updated progress
		var updatedProgress model.UserTaskProgress
		if err := global.GVA_DB.First(&updatedProgress, "user_id = ? AND task_id = ?", userID, taskID).Error; err != nil {
			fmt.Printf("❌ Failed to get updated progress: %v\n", err)
		} else {
			fmt.Printf("✅ Updated progress: Status=%s, Value=%d, CompletionCount=%d\n",
				updatedProgress.Status, updatedProgress.ProgressValue, updatedProgress.CompletionCount)
		}
	}

	fmt.Printf("\n✅ Debug completed!\n")
}

func stringPtr(s string) *string {
	return &s
}
